<template>
  <div class="homepage">
    <!-- Header -->
    <header class="header">
      <nav class="nav-container">
        <div class="logo">
          <h2>🌱 智农商城</h2>
        </div>
        <ul class="nav-menu" :class="{ active: isMenuOpen }">
          <li><a href="#home" @click="closeMenu">首页</a></li>
          <li><a href="#products" @click="closeMenu">产品</a></li>
          <li><a href="#farmers" @click="closeMenu">农户入驻</a></li>
          <li><a href="#features" @click="closeMenu">智能特色</a></li>
          <li><a href="#about" @click="closeMenu">关于我们</a></li>
          <li><a href="#contact" @click="closeMenu">联系我们</a></li>
        </ul>
        <div class="auth-buttons">
          <button class="btn-secondary">登录</button>
          <button class="btn-primary">注册</button>
        </div>
        <div class="hamburger" @click="toggleMenu" :class="{ active: isMenuOpen }">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </nav>
    </header>

    <!-- Hero Section -->
    <section id="home" class="hero">
      <div class="hero-content">
        <div class="hero-text">
          <h1>智慧农业，助农兴农</h1>
          <p>基于Spring Boot + Vue的智能助农电商系统，连接优质农户与消费者，运用AI技术实现精准匹配，让新鲜农产品直达餐桌，助力乡村振兴。</p>
          <div class="hero-buttons">
            <button class="btn-primary large" @click="scrollToSection('products')">立即购买</button>
            <button class="btn-secondary large" @click="scrollToSection('farmers')">农户入驻</button>
          </div>
          <div class="hero-stats">
            <div class="stat-mini">
              <span class="stat-number">1000+</span>
              <span class="stat-label">合作农户</span>
            </div>
            <div class="stat-mini">
              <span class="stat-number">50万+</span>
              <span class="stat-label">服务用户</span>
            </div>
            <div class="stat-mini">
              <span class="stat-number">99.5%</span>
              <span class="stat-label">新鲜度保证</span>
            </div>
          </div>
        </div>
        <div class="hero-image">
          <div class="hero-placeholder">
            <span>🌾</span>
            <p>智慧农业科技</p>
            <div class="tech-badges">
              <span class="badge">AI智能推荐</span>
              <span class="badge">区块链溯源</span>
              <span class="badge">物联网监控</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Featured Products -->
    <section id="products" class="featured-products">
      <div class="container">
        <h2>精选农产品</h2>
        <p class="section-subtitle">从田间到餐桌，新鲜直达</p>

        <!-- Product Categories -->
        <div class="category-tabs">
          <button
            v-for="category in productCategories"
            :key="category.id"
            :class="['category-tab', { active: activeCategory === category.id }]"
            @click="activeCategory = category.id"
          >
            {{ category.icon }} {{ category.name }}
          </button>
        </div>

        <div class="products-grid">
          <div
            class="product-card"
            v-for="product in filteredProducts"
            :key="product.id"
            @click="selectProduct(product)"
          >
            <div class="product-image">
              <span class="product-icon">{{ product.icon }}</span>
              <div class="product-badges">
                <span v-if="product.organic" class="badge organic">有机</span>
                <span v-if="product.fresh" class="badge fresh">新鲜</span>
                <span v-if="product.local" class="badge local">本地</span>
              </div>
            </div>
            <div class="product-info">
              <h3>{{ product.name }}</h3>
              <p class="product-price">¥{{ product.price }}/{{ product.unit }}</p>
              <p class="product-farmer">
                <span class="farmer-icon">👨‍🌾</span>
                {{ product.farmer }}
              </p>
              <div class="product-rating">
                <span class="stars">⭐⭐⭐⭐⭐</span>
                <span class="rating-text">({{ product.rating }})</span>
              </div>
              <div class="product-actions">
                <button class="btn-primary small" @click.stop="addToCart(product)">
                  🛒 加入购物车
                </button>
                <button class="btn-secondary small" @click.stop="viewDetails(product)">
                  详情
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Farmers Section -->
    <section id="farmers" class="farmers-section">
      <div class="container">
        <h2>优质农户</h2>
        <p class="section-subtitle">与我们合作的优秀农户</p>
        <div class="farmers-grid">
          <div class="farmer-card" v-for="farmer in featuredFarmers" :key="farmer.id">
            <div class="farmer-avatar">
              <span>{{ farmer.avatar }}</span>
            </div>
            <div class="farmer-info">
              <h3>{{ farmer.name }}</h3>
              <p class="farmer-location">📍 {{ farmer.location }}</p>
              <p class="farmer-specialty">🌱 {{ farmer.specialty }}</p>
              <div class="farmer-stats">
                <span>{{ farmer.experience }}年经验</span>
                <span>{{ farmer.products }}种产品</span>
              </div>
              <button class="btn-primary small">查看农场</button>
            </div>
          </div>
        </div>

        <div class="farmer-join">
          <h3>成为我们的合作农户</h3>
          <p>加入智农商城，让您的优质农产品走向更广阔的市场</p>
          <button class="btn-primary large">立即入驻</button>
        </div>
      </div>
    </section>

    <!-- Smart Features -->
    <section id="features" class="features">
      <div class="container">
        <h2>智能助农特色</h2>
        <p class="section-subtitle">运用前沿科技，打造智慧农业生态</p>

        <div class="features-grid">
          <div class="feature-card" v-for="feature in smartFeatures" :key="feature.id">
            <div class="feature-icon">{{ feature.icon }}</div>
            <h3>{{ feature.title }}</h3>
            <p>{{ feature.description }}</p>
            <div class="feature-tech">
              <span class="tech-tag" v-for="tech in feature.technologies" :key="tech">
                {{ tech }}
              </span>
            </div>
          </div>
        </div>

        <!-- AI Recommendation Demo -->
        <div class="ai-demo">
          <h3>🤖 AI智能推荐演示</h3>
          <div class="demo-container">
            <div class="user-preferences">
              <h4>用户偏好设置</h4>
              <div class="preference-tags">
                <span
                  v-for="pref in userPreferences"
                  :key="pref"
                  :class="['pref-tag', { active: selectedPreferences.includes(pref) }]"
                  @click="togglePreference(pref)"
                >
                  {{ pref }}
                </span>
              </div>
            </div>
            <div class="ai-recommendations">
              <h4>AI推荐结果</h4>
              <div class="recommendation-list">
                <div
                  v-for="rec in aiRecommendations"
                  :key="rec.id"
                  class="recommendation-item"
                >
                  <span class="rec-icon">{{ rec.icon }}</span>
                  <div class="rec-info">
                    <span class="rec-name">{{ rec.name }}</span>
                    <span class="rec-reason">{{ rec.reason }}</span>
                  </div>
                  <span class="rec-score">{{ rec.score }}%匹配</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Statistics -->
    <section class="stats">
      <div class="container">
        <div class="stats-grid">
          <div class="stat-item" v-for="stat in statistics" :key="stat.id">
            <div class="stat-icon">{{ stat.icon }}</div>
            <h3>{{ stat.number }}</h3>
            <p>{{ stat.label }}</p>
            <span class="stat-trend">{{ stat.trend }}</span>
          </div>
        </div>
      </div>
    </section>

    <!-- Technology Showcase -->
    <section class="tech-showcase">
      <div class="container">
        <h2>技术架构展示</h2>
        <p class="section-subtitle">基于Spring Boot + Vue的现代化技术栈</p>

        <div class="tech-stack">
          <div class="tech-layer">
            <h3>前端技术</h3>
            <div class="tech-items">
              <div class="tech-item">
                <span class="tech-icon">⚡</span>
                <span class="tech-name">Vue 3</span>
              </div>
              <div class="tech-item">
                <span class="tech-icon">🎨</span>
                <span class="tech-name">Element Plus</span>
              </div>
              <div class="tech-item">
                <span class="tech-icon">📱</span>
                <span class="tech-name">响应式设计</span>
              </div>
            </div>
          </div>

          <div class="tech-layer">
            <h3>后端技术</h3>
            <div class="tech-items">
              <div class="tech-item">
                <span class="tech-icon">🍃</span>
                <span class="tech-name">Spring Boot</span>
              </div>
              <div class="tech-item">
                <span class="tech-icon">🗄️</span>
                <span class="tech-name">MySQL</span>
              </div>
              <div class="tech-item">
                <span class="tech-icon">🔒</span>
                <span class="tech-name">Spring Security</span>
              </div>
            </div>
          </div>

          <div class="tech-layer">
            <h3>智能技术</h3>
            <div class="tech-items">
              <div class="tech-item">
                <span class="tech-icon">🤖</span>
                <span class="tech-name">AI推荐算法</span>
              </div>
              <div class="tech-item">
                <span class="tech-icon">🔗</span>
                <span class="tech-name">区块链溯源</span>
              </div>
              <div class="tech-item">
                <span class="tech-icon">📊</span>
                <span class="tech-name">大数据分析</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-section">
            <h3>🌱 智农商城</h3>
            <p>基于Spring Boot + Vue的智能助农电商系统，连接农户与消费者，助力乡村振兴。</p>
            <div class="social-links">
              <a href="#" class="social-link">📱 微信</a>
              <a href="#" class="social-link">📺 抖音</a>
              <a href="#" class="social-link">📧 邮箱</a>
            </div>
          </div>
          <div class="footer-section">
            <h4>快速导航</h4>
            <ul>
              <li><a href="#products">精选产品</a></li>
              <li><a href="#farmers">农户入驻</a></li>
              <li><a href="#features">智能特色</a></li>
              <li><a href="#about">关于我们</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>客户服务</h4>
            <ul>
              <li><a href="#">帮助中心</a></li>
              <li><a href="#">配送说明</a></li>
              <li><a href="#">退换货政策</a></li>
              <li><a href="#">常见问题</a></li>
              <li><a href="#">质量保证</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>联系我们</h4>
            <p>📧 <EMAIL></p>
            <p>📞 400-888-6666</p>
            <p>📍 北京市海淀区中关村科技园</p>
            <p>🕒 服务时间：7×24小时</p>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2024 智农商城 - 基于Spring Boot + Vue的智能助农电商系统. 京ICP备2024000001号</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue'

export default {
  name: 'HomePage',
  setup() {
    const isMenuOpen = ref(false)
    const activeCategory = ref('all')
    const selectedPreferences = ref(['有机', '本地'])

    const productCategories = ref([
      { id: 'all', name: '全部', icon: '🌾' },
      { id: 'vegetables', name: '蔬菜', icon: '🥬' },
      { id: 'fruits', name: '水果', icon: '🍎' },
      { id: 'grains', name: '谷物', icon: '🌽' },
      { id: 'dairy', name: '蛋奶', icon: '🥚' }
    ])

    const featuredProducts = ref([
      {
        id: 1, name: '有机番茄', price: '12.99', unit: '斤', farmer: '绿谷生态农场',
        icon: '🍅', category: 'vegetables', organic: true, fresh: true, local: true,
        rating: 4.9, description: '自然成熟，口感鲜美'
      },
      {
        id: 2, name: '新鲜生菜', price: '8.49', unit: '颗', farmer: '阳光有机农庄',
        icon: '🥬', category: 'vegetables', organic: true, fresh: true, local: false,
        rating: 4.8, description: '脆嫩多汁，营养丰富'
      },
      {
        id: 3, name: '甜玉米', price: '15.99', unit: '打', farmer: '丰收家庭农场',
        icon: '🌽', category: 'grains', organic: false, fresh: true, local: true,
        rating: 4.7, description: '香甜可口，粒粒饱满'
      },
      {
        id: 4, name: '土鸡蛋', price: '25.99', unit: '打', farmer: '快乐母鸡养殖场',
        icon: '🥚', category: 'dairy', organic: true, fresh: true, local: true,
        rating: 4.9, description: '散养土鸡，营养价值高'
      },
      {
        id: 5, name: '有机胡萝卜', price: '9.49', unit: '斤', farmer: '根茎蔬菜专业社',
        icon: '🥕', category: 'vegetables', organic: true, fresh: true, local: false,
        rating: 4.6, description: '色泽鲜艳，甜脆可口'
      },
      {
        id: 6, name: '红富士苹果', price: '18.49', unit: '斤', farmer: '山地果园合作社',
        icon: '🍎', category: 'fruits', organic: false, fresh: true, local: true,
        rating: 4.8, description: '脆甜多汁，果香浓郁'
      }
    ])

    const featuredFarmers = ref([
      {
        id: 1, name: '张大伯', avatar: '👨‍🌾', location: '山东寿光',
        specialty: '有机蔬菜种植', experience: 15, products: 12
      },
      {
        id: 2, name: '李阿姨', avatar: '👩‍🌾', location: '陕西洛川',
        specialty: '优质苹果种植', experience: 20, products: 8
      },
      {
        id: 3, name: '王师傅', avatar: '👨‍🌾', location: '黑龙江五常',
        specialty: '有机大米种植', experience: 25, products: 5
      }
    ])

    const smartFeatures = ref([
      {
        id: 1, icon: '🤖', title: 'AI智能推荐',
        description: '基于用户偏好和购买历史，智能推荐最适合的农产品',
        technologies: ['机器学习', '协同过滤', '深度学习']
      },
      {
        id: 2, icon: '🔗', title: '区块链溯源',
        description: '全程可追溯的供应链管理，确保食品安全和品质',
        technologies: ['区块链', '物联网', '二维码']
      },
      {
        id: 3, icon: '📊', title: '大数据分析',
        description: '实时分析市场需求，优化农产品供应链和定价策略',
        technologies: ['大数据', '云计算', '数据挖掘']
      },
      {
        id: 4, icon: '📱', title: '移动端优化',
        description: '响应式设计，支持多端访问，随时随地购买新鲜农产品',
        technologies: ['Vue3', 'PWA', '响应式设计']
      }
    ])

    const statistics = ref([
      { id: 1, number: '1000+', label: '合作农户', icon: '👨‍🌾', trend: '↗ 月增长15%' },
      { id: 2, number: '50万+', label: '注册用户', icon: '👥', trend: '↗ 月增长25%' },
      { id: 3, number: '100万+', label: '订单完成', icon: '📦', trend: '↗ 月增长30%' },
      { id: 4, number: '99.5%', label: '用户满意度', icon: '⭐', trend: '↗ 持续提升' }
    ])

    const userPreferences = ref(['有机', '本地', '新鲜', '价格优惠', '品牌认证', '快速配送'])

    const aiRecommendations = ref([
      { id: 1, name: '有机番茄', icon: '🍅', reason: '基于您的有机偏好', score: 95 },
      { id: 2, name: '本地生菜', icon: '🥬', reason: '本地新鲜直供', score: 88 },
      { id: 3, name: '土鸡蛋', icon: '🥚', reason: '高营养价值推荐', score: 92 }
    ])

    const filteredProducts = computed(() => {
      if (activeCategory.value === 'all') {
        return featuredProducts.value
      }
      return featuredProducts.value.filter(product => product.category === activeCategory.value)
    })

    const toggleMenu = () => {
      isMenuOpen.value = !isMenuOpen.value
    }

    const closeMenu = () => {
      isMenuOpen.value = false
    }

    const scrollToSection = (sectionId) => {
      const element = document.getElementById(sectionId)
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' })
      }
    }

    const selectProduct = (product) => {
      console.log('选择产品:', product.name)
      // 这里可以添加产品详情页面跳转逻辑
    }

    const addToCart = (product) => {
      console.log('添加到购物车:', product.name)
      // 这里可以添加购物车逻辑
      alert(`${product.name} 已添加到购物车！`)
    }

    const viewDetails = (product) => {
      console.log('查看详情:', product.name)
      // 这里可以添加产品详情页面逻辑
    }

    const togglePreference = (preference) => {
      const index = selectedPreferences.value.indexOf(preference)
      if (index > -1) {
        selectedPreferences.value.splice(index, 1)
      } else {
        selectedPreferences.value.push(preference)
      }
      // 模拟AI推荐更新
      updateAIRecommendations()
    }

    const updateAIRecommendations = () => {
      // 简单的推荐逻辑演示
      const newRecommendations = []
      if (selectedPreferences.value.includes('有机')) {
        newRecommendations.push({ id: 1, name: '有机番茄', icon: '🍅', reason: '符合有机偏好', score: 95 })
      }
      if (selectedPreferences.value.includes('本地')) {
        newRecommendations.push({ id: 2, name: '本地玉米', icon: '🌽', reason: '本地新鲜直供', score: 90 })
      }
      if (selectedPreferences.value.includes('新鲜')) {
        newRecommendations.push({ id: 3, name: '新鲜苹果', icon: '🍎', reason: '当日采摘', score: 88 })
      }
      aiRecommendations.value = newRecommendations.slice(0, 3)
    }

    onMounted(() => {
      // Smooth scrolling for navigation links
      document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
          e.preventDefault()
          const target = document.querySelector(this.getAttribute('href'))
          if (target) {
            target.scrollIntoView({ behavior: 'smooth' })
          }
        })
      })
    })

    return {
      isMenuOpen,
      activeCategory,
      selectedPreferences,
      productCategories,
      featuredProducts,
      featuredFarmers,
      smartFeatures,
      statistics,
      userPreferences,
      aiRecommendations,
      filteredProducts,
      toggleMenu,
      closeMenu,
      scrollToSection,
      selectProduct,
      addToCart,
      viewDetails,
      togglePreference
    }
  }
}
</script>

<style scoped>
/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.homepage {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #333;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Buttons */
.btn-primary, .btn-secondary {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn-primary {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #45a049, #3d8b40);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.btn-secondary {
  background: transparent;
  color: #4CAF50;
  border: 2px solid #4CAF50;
}

.btn-secondary:hover {
  background: #4CAF50;
  color: white;
}

.btn-primary.large, .btn-secondary.large {
  padding: 16px 32px;
  font-size: 18px;
}

.btn-primary.small {
  padding: 8px 16px;
  font-size: 14px;
}

/* Header */
.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 1000;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

.logo h2 {
  color: #4CAF50;
  font-size: 24px;
  font-weight: 700;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 30px;
}

.nav-menu a {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-menu a:hover {
  color: #4CAF50;
}

.auth-buttons {
  display: flex;
  gap: 15px;
}

.hamburger {
  display: none;
  flex-direction: column;
  cursor: pointer;
}

.hamburger span {
  width: 25px;
  height: 3px;
  background: #333;
  margin: 3px 0;
  transition: 0.3s;
}

/* Hero Section */
.hero {
  background: linear-gradient(135deg, #f8fffe 0%, #e8f5e8 100%);
  padding: 120px 0 80px;
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.hero-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.hero-text h1 {
  font-size: 3.5rem;
  font-weight: 700;
  color: #2c5530;
  margin-bottom: 20px;
  line-height: 1.2;
}

.hero-text p {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 30px;
  line-height: 1.6;
}

.hero-buttons {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.hero-image {
  display: flex;
  justify-content: center;
}

.hero-placeholder {
  background: white;
  border-radius: 20px;
  padding: 60px;
  text-align: center;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  border: 3px solid #e8f5e8;
}

.hero-placeholder span {
  font-size: 4rem;
  display: block;
  margin-bottom: 20px;
}

.hero-placeholder p {
  color: #4CAF50;
  font-weight: 600;
  font-size: 1.1rem;
  margin-bottom: 20px;
}

.tech-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
}

.badge {
  background: #4CAF50;
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.hero-stats {
  display: flex;
  gap: 30px;
  margin-top: 30px;
  justify-content: flex-start;
}

.stat-mini {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: #4CAF50;
}

.stat-label {
  font-size: 0.9rem;
  color: #666;
}

/* Featured Products */
.featured-products {
  padding: 80px 0;
  background: white;
}

.featured-products h2 {
  text-align: center;
  font-size: 2.5rem;
  color: #2c5530;
  margin-bottom: 10px;
}

.section-subtitle {
  text-align: center;
  color: #666;
  font-size: 1.1rem;
  margin-bottom: 50px;
}

.category-tabs {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-bottom: 40px;
  flex-wrap: wrap;
}

.category-tab {
  padding: 10px 20px;
  border: 2px solid #e0e0e0;
  background: white;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.category-tab:hover, .category-tab.active {
  border-color: #4CAF50;
  background: #4CAF50;
  color: white;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 30px;
}

.product-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid #f0f0f0;
  cursor: pointer;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
}

.product-image {
  text-align: center;
  margin-bottom: 20px;
  position: relative;
}

.product-icon {
  font-size: 3rem;
  display: block;
  margin-bottom: 10px;
}

.product-badges {
  display: flex;
  gap: 5px;
  justify-content: center;
  flex-wrap: wrap;
}

.badge.organic {
  background: #4CAF50;
}

.badge.fresh {
  background: #FF9800;
}

.badge.local {
  background: #2196F3;
}

.product-info h3 {
  font-size: 1.3rem;
  color: #2c5530;
  margin-bottom: 8px;
}

.product-price {
  font-size: 1.4rem;
  font-weight: 700;
  color: #4CAF50;
  margin-bottom: 5px;
}

.product-farmer {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.farmer-icon {
  font-size: 1rem;
}

.product-rating {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 15px;
}

.stars {
  color: #FFD700;
  font-size: 0.9rem;
}

.rating-text {
  color: #666;
  font-size: 0.8rem;
}

.product-actions {
  display: flex;
  gap: 10px;
}

/* Farmers Section */
.farmers-section {
  padding: 80px 0;
  background: #f8fffe;
}

.farmers-section h2 {
  text-align: center;
  font-size: 2.5rem;
  color: #2c5530;
  margin-bottom: 10px;
}

.farmers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
  margin-bottom: 60px;
}

.farmer-card {
  background: white;
  border-radius: 15px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease;
}

.farmer-card:hover {
  transform: translateY(-5px);
}

.farmer-avatar {
  font-size: 4rem;
  margin-bottom: 20px;
}

.farmer-info h3 {
  font-size: 1.4rem;
  color: #2c5530;
  margin-bottom: 10px;
}

.farmer-location, .farmer-specialty {
  color: #666;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.farmer-stats {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin: 15px 0;
  font-size: 0.9rem;
  color: #4CAF50;
}

.farmer-join {
  background: white;
  border-radius: 15px;
  padding: 40px;
  text-align: center;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.08);
}

.farmer-join h3 {
  font-size: 1.8rem;
  color: #2c5530;
  margin-bottom: 15px;
}

.farmer-join p {
  color: #666;
  margin-bottom: 25px;
  font-size: 1.1rem;
}

/* Smart Features Section */
.features {
  padding: 80px 0;
  background: white;
}

.features h2 {
  text-align: center;
  font-size: 2.5rem;
  color: #2c5530;
  margin-bottom: 10px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 40px;
  margin-bottom: 60px;
}

.feature-card {
  text-align: center;
  padding: 30px 20px;
  background: #f8fffe;
  border-radius: 15px;
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 20px;
}

.feature-card h3 {
  font-size: 1.4rem;
  color: #2c5530;
  margin-bottom: 15px;
}

.feature-card p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 15px;
}

.feature-tech {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
}

.tech-tag {
  background: #4CAF50;
  color: white;
  padding: 4px 10px;
  border-radius: 15px;
  font-size: 0.8rem;
}

/* AI Demo */
.ai-demo {
  background: white;
  border-radius: 15px;
  padding: 40px;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.08);
}

.ai-demo h3 {
  text-align: center;
  color: #2c5530;
  margin-bottom: 30px;
  font-size: 1.6rem;
}

.demo-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
}

.user-preferences h4, .ai-recommendations h4 {
  color: #2c5530;
  margin-bottom: 20px;
  font-size: 1.2rem;
}

.preference-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.pref-tag {
  padding: 8px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.pref-tag:hover, .pref-tag.active {
  border-color: #4CAF50;
  background: #4CAF50;
  color: white;
}

.recommendation-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.recommendation-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: #f8fffe;
  border-radius: 10px;
  border-left: 4px solid #4CAF50;
}

.rec-icon {
  font-size: 2rem;
}

.rec-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.rec-name {
  font-weight: 600;
  color: #2c5530;
}

.rec-reason {
  font-size: 0.9rem;
  color: #666;
}

.rec-score {
  font-weight: 600;
  color: #4CAF50;
  font-size: 0.9rem;
}

/* Statistics */
.stats {
  padding: 60px 0;
  background: #4CAF50;
  color: white;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 40px;
  text-align: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-icon {
  font-size: 2.5rem;
  margin-bottom: 15px;
}

.stat-item h3 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 10px;
}

.stat-item p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin-bottom: 5px;
}

.stat-trend {
  font-size: 0.9rem;
  opacity: 0.8;
  color: #e8f5e8;
}

/* Technology Showcase */
.tech-showcase {
  padding: 80px 0;
  background: #f8fffe;
}

.tech-showcase h2 {
  text-align: center;
  font-size: 2.5rem;
  color: #2c5530;
  margin-bottom: 10px;
}

.tech-stack {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
}

.tech-layer {
  background: white;
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.08);
  text-align: center;
}

.tech-layer h3 {
  color: #2c5530;
  margin-bottom: 25px;
  font-size: 1.4rem;
}

.tech-items {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.tech-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: #f8fffe;
  border-radius: 10px;
  transition: transform 0.3s ease;
}

.tech-item:hover {
  transform: translateX(5px);
}

.tech-icon {
  font-size: 1.5rem;
}

.tech-name {
  font-weight: 600;
  color: #2c5530;
}

/* Footer */
.footer {
  background: #2c5530;
  color: white;
  padding: 50px 0 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-bottom: 30px;
}

.footer-section h3, .footer-section h4 {
  margin-bottom: 20px;
  color: #4CAF50;
}

.footer-section p {
  line-height: 1.6;
  margin-bottom: 15px;
}

.social-links {
  display: flex;
  gap: 15px;
  margin-top: 15px;
}

.social-link {
  color: #ccc;
  text-decoration: none;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.social-link:hover {
  color: #4CAF50;
  background: rgba(76, 175, 80, 0.1);
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: 8px;
}

.footer-section a {
  color: #ccc;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-section a:hover {
  color: #4CAF50;
}

.footer-bottom {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #444;
  color: #ccc;
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-menu {
    position: fixed;
    left: -100%;
    top: 70px;
    flex-direction: column;
    background-color: white;
    width: 100%;
    text-align: center;
    transition: 0.3s;
    box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
    padding: 20px 0;
  }

  .nav-menu.active {
    left: 0;
  }

  .hamburger {
    display: flex;
  }

  .hamburger.active span:nth-child(2) {
    opacity: 0;
  }

  .hamburger.active span:nth-child(1) {
    transform: translateY(8px) rotate(45deg);
  }

  .hamburger.active span:nth-child(3) {
    transform: translateY(-8px) rotate(-45deg);
  }

  .auth-buttons {
    display: none;
  }

  .hero-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .hero-text h1 {
    font-size: 2.5rem;
  }

  .hero-buttons {
    justify-content: center;
  }

  .hero-stats {
    justify-content: center;
  }

  .category-tabs {
    justify-content: center;
  }

  .products-grid {
    grid-template-columns: 1fr;
  }

  .farmers-grid {
    grid-template-columns: 1fr;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .demo-container {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .tech-stack {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .social-links {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .hero-text h1 {
    font-size: 2rem;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .btn-primary.large, .btn-secondary.large {
    width: 100%;
    max-width: 300px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }
}

/* Smooth Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.product-card, .feature-card {
  animation: fadeInUp 0.6s ease-out;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus styles for accessibility */
button:focus, a:focus {
  outline: 2px solid #4CAF50;
  outline-offset: 2px;
}
</style>
